package database

import (
    "fmt"
    "log"
    
    "goHomework/config"
    "goHomework/model"
    
    "gorm.io/driver/mysql"
    "gorm.io/gorm"
)

func NewMySQLConnection(cfg *config.DatabaseConfig) (*gorm.DB, error) {
    dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
        cfg.Username, cfg.Password, cfg.Host, cfg.Port, cfg.Database)
    
    db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
    if err != nil {
        return nil, fmt.Errorf("failed to connect to MySQL: %v", err)
    }
    
    // 自动迁移表结构
    err = db.AutoMigrate(&model.Customer{})
    if err != nil {
        return nil, fmt.Errorf("failed to migrate database: %v", err)
    }
    
    log.Printf("✅ 成功连接到 MySQL 数据库: %s", cfg.Database)
    return db, nil
}