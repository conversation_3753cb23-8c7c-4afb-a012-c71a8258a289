{"name": "@csstools/postcss-normalize-display-values", "description": "Use two values display syntax for inner and outer display types.", "version": "1.0.1", "author": "<PERSON> <<EMAIL>>", "license": "CC0-1.0", "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "engines": {"node": "^12 || ^14 || >=16"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs", "default": "./dist/index.mjs"}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "dependencies": {"postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.2"}, "scripts": {"build": "rollup -c ../../rollup/default.js", "clean": "node -e \"fs.rmSync('./dist', { recursive: true, force: true });\"", "docs": "node ../../.github/bin/generate-docs/install.mjs", "lint": "npm run lint:eslint && npm run lint:package-json", "lint:eslint": "eslint ./src --ext .js --ext .ts --ext .mjs --no-error-on-unmatched-pattern", "lint:package-json": "node ../../.github/bin/format-package-json.mjs", "prepublishOnly": "npm run clean && npm run build && npm run test", "test": "node .tape.mjs && npm run test:exports", "test:exports": "node ./test/_import.mjs && node ./test/_require.cjs", "test:rewrite-expects": "REWRITE_EXPECTS=true node .tape.mjs"}, "repository": {"type": "git", "url": "https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-normalize-display-values"}, "keywords": ["block", "css", "display", "flex", "flow", "flow-root", "grid", "inline", "inline-block", "inline-flex", "inline-grid", "inline-table", "list-item", "postcss-plugin", "ruby", "ruby-base", "ruby-text", "run-in", "table", "table-caption", "table-cell"], "csstools": {"exportName": "postcssNormalizeDisplayValues", "humanReadableName": "PostCSS Normalize Display Values"}, "volta": {"extends": "../../package.json"}}