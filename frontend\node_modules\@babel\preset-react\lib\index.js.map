{"version": 3, "file": "index.js", "sources": ["../src/normalize-options.ts", "../src/index.ts"], "sourcesContent": ["import {\n  OptionValidator,\n  findSuggestion,\n} from \"@babel/helper-validator-option\";\nconst v = new OptionValidator(\"@babel/preset-react\");\n\nexport default function normalizeOptions(options: any = {}) {\n  if (process.env.BABEL_8_BREAKING) {\n    if (\"useSpread\" in options) {\n      throw new Error(\n        '@babel/preset-react: Since Babel 8, an inline object with spread elements is always used, and the \"useSpread\" option is no longer available. Please remove it from your config.',\n      );\n    }\n\n    if (\"useBuiltIns\" in options) {\n      const useBuiltInsFormatted = JSON.stringify(options.useBuiltIns);\n      throw new Error(\n        `@babel/preset-react: Since \"useBuiltIns\" is removed in Babel 8, you can remove it from the config.\n- Babel 8 now transforms JSX spread to object spread. If you need to transpile object spread with\n\\`useBuiltIns: ${useBuiltInsFormatted}\\`, you can use the following config\n{\n  \"plugins\": [\n    [\"@babel/plugin-transform-object-rest-spread\", { \"loose\": true, \"useBuiltIns\": ${useBuiltInsFormatted} }]\n  ],\n  \"presets\": [\"@babel/preset-react\"]\n}`,\n      );\n    }\n\n    const TopLevelOptions = {\n      development: \"development\",\n      importSource: \"importSource\",\n      pragma: \"pragma\",\n      pragmaFrag: \"pragmaFrag\",\n      pure: \"pure\",\n      runtime: \"runtime\",\n      throwIfNamespace: \"throwIfNamespace\",\n    };\n    v.validateTopLevelOptions(options, TopLevelOptions);\n    const development = v.validateBooleanOption(\n      TopLevelOptions.development,\n      options.development,\n    );\n    let importSource = v.validateStringOption(\n      TopLevelOptions.importSource,\n      options.importSource,\n    );\n    let pragma = v.validateStringOption(TopLevelOptions.pragma, options.pragma);\n    let pragmaFrag = v.validateStringOption(\n      TopLevelOptions.pragmaFrag,\n      options.pragmaFrag,\n    );\n    const pure = v.validateBooleanOption(TopLevelOptions.pure, options.pure);\n    const runtime = v.validateStringOption(\n      TopLevelOptions.runtime,\n      options.runtime,\n      \"automatic\",\n    );\n    const throwIfNamespace = v.validateBooleanOption(\n      TopLevelOptions.throwIfNamespace,\n      options.throwIfNamespace,\n      true,\n    );\n\n    const validRuntime = [\"classic\", \"automatic\"];\n\n    if (runtime === \"classic\") {\n      pragma = pragma || \"React.createElement\";\n      pragmaFrag = pragmaFrag || \"React.Fragment\";\n    } else if (runtime === \"automatic\") {\n      importSource = importSource || \"react\";\n    } else {\n      throw new Error(\n        `@babel/preset-react: 'runtime' must be one of ['automatic', 'classic'] but we have '${runtime}'\\n` +\n          `- Did you mean '${findSuggestion(runtime, validRuntime)}'?`,\n      );\n    }\n\n    return {\n      development,\n      importSource,\n      pragma,\n      pragmaFrag,\n      pure,\n      runtime,\n      throwIfNamespace,\n    };\n  } else {\n    let { pragma, pragmaFrag } = options;\n\n    const {\n      pure,\n      throwIfNamespace = true,\n      runtime = \"classic\",\n      importSource,\n      useBuiltIns,\n      useSpread,\n    } = options;\n\n    if (runtime === \"classic\") {\n      pragma = pragma || \"React.createElement\";\n      pragmaFrag = pragmaFrag || \"React.Fragment\";\n    }\n\n    const development =\n      options.development == null ? undefined : !!options.development;\n\n    return {\n      development,\n      importSource,\n      pragma,\n      pragmaFrag,\n      pure,\n      runtime,\n      throwIfNamespace,\n      useBuiltIns,\n      useSpread,\n    };\n  }\n}\n", "import { declarePreset } from \"@babel/helper-plugin-utils\";\nimport transformReactJSX from \"@babel/plugin-transform-react-jsx\";\nimport transformReactJSXDevelopment from \"@babel/plugin-transform-react-jsx-development\";\nimport transformReactDisplayName from \"@babel/plugin-transform-react-display-name\";\nimport transformReactPure from \"@babel/plugin-transform-react-pure-annotations\";\nimport normalizeOptions from \"./normalize-options.ts\";\n\nexport interface Options {\n  development?: boolean;\n  importSource?: string;\n  pragma?: string;\n  pragmaFrag?: string;\n  pure?: string;\n  runtime?: \"automatic\" | \"classic\";\n  throwIfNamespace?: boolean;\n  useBuiltIns?: boolean;\n  useSpread?: boolean;\n}\n\nexport default declarePreset((api, opts: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  const {\n    development = process.env.BABEL_8_BREAKING\n      ? api.env(env => env === \"development\")\n      : false,\n    importSource,\n    pragma,\n    pragmaFrag,\n    pure,\n    runtime,\n    throwIfNamespace,\n  } = normalizeOptions(opts);\n\n  return {\n    plugins: [\n      [\n        development ? transformReactJSXDevelopment : transformReactJSX,\n        process.env.BABEL_8_BREAKING\n          ? {\n              importSource,\n              pragma,\n              pragmaFrag,\n              runtime,\n              throwIfNamespace,\n              pure,\n            }\n          : {\n              importSource,\n              pragma,\n              pragmaFrag,\n              runtime,\n              throwIfNamespace,\n              pure,\n              useBuiltIns: !!opts.useBuiltIns,\n              useSpread: opts.useSpread,\n            },\n      ],\n      transformReactDisplayName,\n      pure !== false && transformReactPure,\n    ].filter(Boolean),\n  };\n});\n"], "names": ["OptionValidator", "normalizeOptions", "options", "pragma", "pragmaFrag", "pure", "throwIfNamespace", "runtime", "importSource", "useBuiltIns", "useSpread", "development", "undefined", "declarePreset", "api", "opts", "assertVersion", "plugins", "transformReactJSXDevelopment", "transformReactJSX", "transformReactDisplayName", "transformReactPure", "filter", "Boolean"], "mappings": ";;;;;;;;;;;;;;;;;;AAIU,IAAIA,qCAAe,CAAC,qBAAqB,EAAC;AAErC,SAASC,gBAAgBA,CAACC,OAAY,GAAG,EAAE,EAAE;AAiFnD,EAAA;IACL,IAAI;MAAEC,MAAM;AAAEC,MAAAA,UAAAA;AAAW,KAAC,GAAGF,OAAO,CAAA;IAEpC,MAAM;MACJG,IAAI;AACJC,MAAAA,gBAAgB,GAAG,IAAI;AACvBC,MAAAA,OAAO,GAAG,SAAS;MACnBC,YAAY;MACZC,WAAW;AACXC,MAAAA,SAAAA;AACF,KAAC,GAAGR,OAAO,CAAA;IAEX,IAAIK,OAAO,KAAK,SAAS,EAAE;MACzBJ,MAAM,GAAGA,MAAM,IAAI,qBAAqB,CAAA;MACxCC,UAAU,GAAGA,UAAU,IAAI,gBAAgB,CAAA;AAC7C,KAAA;AAEA,IAAA,MAAMO,WAAW,GACfT,OAAO,CAACS,WAAW,IAAI,IAAI,GAAGC,SAAS,GAAG,CAAC,CAACV,OAAO,CAACS,WAAW,CAAA;IAEjE,OAAO;MACLA,WAAW;MACXH,YAAY;MACZL,MAAM;MACNC,UAAU;MACVC,IAAI;MACJE,OAAO;MACPD,gBAAgB;MAChBG,WAAW;AACXC,MAAAA,SAAAA;KACD,CAAA;AACH,GAAA;AACF;;ACpGA,YAAeG,+BAAa,CAAC,CAACC,GAAG,EAAEC,IAAa,KAAK;AACnDD,EAAAA,GAAG,CAACE,aAAa,CAAkB,CAAE,CAAC,CAAA;EAEtC,MAAM;AACJL,IAAAA,WAAW,GAEP,KAAK;IACTH,YAAY;IACZL,MAAM;IACNC,UAAU;IACVC,IAAI;IACJE,OAAO;AACPD,IAAAA,gBAAAA;AACF,GAAC,GAAGL,gBAAgB,CAACc,IAAI,CAAC,CAAA;EAE1B,OAAO;IACLE,OAAO,EAAE,CACP,CACEN,WAAW,GAAGO,6CAA4B,GAAGC,kCAAiB,EAU1D;MACEX,YAAY;MACZL,MAAM;MACNC,UAAU;MACVG,OAAO;MACPD,gBAAgB;MAChBD,IAAI;AACJI,MAAAA,WAAW,EAAE,CAAC,CAACM,IAAI,CAACN,WAAW;MAC/BC,SAAS,EAAEK,IAAI,CAACL,SAAAA;AAClB,KAAC,CACN,EACDU,0CAAyB,EACzBf,IAAI,KAAK,KAAK,IAAIgB,mCAAkB,CACrC,CAACC,MAAM,CAACC,OAAO,CAAA;GACjB,CAAA;AACH,CAAC,CAAC;;;;"}