# 服务器配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
GIN_MODE=debug
SERVER_READ_TIMEOUT=30s
SERVER_WRITE_TIMEOUT=30s

# 数据库配置
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=123456
DB_DATABASE=springbootds
DB_SSL_MODE=disable

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json
LOG_OUTPUT=stdout
LOG_FILENAME=app.log
LOG_MAX_SIZE=100
LOG_MAX_BACKUPS=3
LOG_MAX_AGE=28
LOG_SKIP_PATHS=/health,/metrics

# CORS配置
CORS_ALLOW_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:8080
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS,PATCH
CORS_ALLOW_HEADERS=Origin,Content-Type,Accept,Authorization,X-Requested-With,X-Request-ID
CORS_EXPOSE_HEADERS=
CORS_ALLOW_CREDENTIALS=true
CORS_MAX_AGE=86400

# 速率限制配置
RATE_LIMIT_ENABLED=true
RATE_LIMIT_RATE=100
RATE_LIMIT_WINDOW=1m
